# 🚀 Magentic-UI Docker 部署完整方案

## 📋 部署文件清单

我已经为您创建了完整的 Docker 部署方案，包含以下文件：

### 核心部署文件
- `docker-compose.yml` - 生产环境配置（PostgreSQL + 完整功能）
- `docker-compose.simple.yml` - 简化配置（SQLite + 快速启动）
- `docker-compose.dev.yml` - 开发环境配置（热重载 + 调试工具）
- `Dockerfile` - 生产环境镜像构建
- `Dockerfile.dev` - 开发环境镜像构建

### 配置文件
- `.env.example` - 环境变量模板
- `config/config.yaml` - 模型和智能体配置
- `.dockerignore` - Docker 构建忽略文件

### 部署脚本
- `deploy.sh` - 主部署脚本（完整功能）
- `quick-start.sh` - 快速启动脚本（新手友好）
- `scripts/backup.sh` - 数据备份脚本
- `scripts/restore.sh` - 数据恢复脚本
- `scripts/init-db.sql` - 数据库初始化脚本

### 文档
- `README-DOCKER.md` - 详细部署文档
- `DEPLOYMENT-SUMMARY.md` - 本文件（部署总结）

## 🎯 三种部署方式

### 1. 🚀 超级快速启动（推荐新手）

```bash
# 1. 克隆项目
git clone https://github.com/microsoft/magentic-ui.git
cd magentic-ui

# 2. 运行快速启动脚本
./quick-start.sh
```

这个脚本会：
- ✅ 自动检查系统要求
- ✅ 引导您配置 OpenAI API Key
- ✅ 让您选择部署模式
- ✅ 自动创建目录和启动服务
- ✅ 显示访问地址和使用提示

### 2. 🛠️ 标准部署（推荐生产）

```bash
# 1. 配置环境
cp .env.example .env
nano .env  # 设置 OPENAI_API_KEY

# 2. 启动服务
./deploy.sh start

# 3. 访问应用
# 主应用: http://localhost:8081
# 浏览器 VNC: http://localhost:6080
```

### 3. 💻 开发模式（推荐开发者）

```bash
# 1. 配置环境
cp .env.example .env
nano .env

# 2. 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 3. 启动前端开发服务器（可选）
docker-compose -f docker-compose.dev.yml --profile frontend up -d

# 4. 启动管理工具（可选）
docker-compose -f docker-compose.dev.yml --profile tools up -d
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | http://localhost:8081 | Magentic-UI 主界面 |
| 浏览器 VNC | http://localhost:6080 | 观看智能体操作浏览器 |
| 数据库 | localhost:5432 | PostgreSQL 数据库 |
| Redis | localhost:6379 | 缓存服务 |
| 前端开发 | http://localhost:8000 | 前端开发服务器（开发模式） |
| PgAdmin | http://localhost:5050 | 数据库管理（开发模式） |
| 日志聚合 | http://localhost:8080 | 日志查看（开发模式） |

## 📊 系统要求

### 最低要求
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 推荐配置
- **CPU**: 4 核心或更多
- **内存**: 8GB RAM 或更多
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接（访问 OpenAI API）

## 🔧 管理命令

### 基本操作
```bash
# 启动服务
./deploy.sh start

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs magentic-ui

# 构建镜像
./deploy.sh build

# 清理资源
./deploy.sh clean
```

### 数据管理
```bash
# 备份数据
./scripts/backup.sh

# 恢复数据库
./scripts/restore.sh -d database_backup.sql

# 恢复工作空间
./scripts/restore.sh -w workspace_backup.tar.gz

# 恢复所有数据
./scripts/restore.sh -a
```

### Docker Compose 命令
```bash
# 查看服务状态
docker-compose ps

# 查看资源使用
docker stats

# 进入容器
docker-compose exec magentic-ui bash

# 查看容器日志
docker-compose logs -f magentic-ui
```

## 🔒 安全配置

### 生产环境建议

1. **更改默认密码**
   ```bash
   # 在 .env 中设置强密码
   POSTGRES_PASSWORD=your-strong-password-here
   ```

2. **限制网络访问**
   ```yaml
   # 在 docker-compose.yml 中
   ports:
     - "127.0.0.1:8081:8081"  # 只允许本地访问
   ```

3. **使用 HTTPS**
   - 配置反向代理（Nginx/Traefik）
   - 使用 Let's Encrypt 证书

4. **API Key 安全**
   - 使用环境变量存储敏感信息
   - 定期轮换 API Key
   - 监控 API 使用情况

## 🐛 常见问题解决

### 1. Docker 权限问题
```bash
sudo usermod -aG docker $USER
# 重新登录或重启
```

### 2. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8081

# 修改 .env 中的端口
_PORT=8082
```

### 3. 内存不足
```bash
# 检查内存使用
free -h
docker stats

# 增加 swap 空间或升级硬件
```

### 4. 浏览器容器启动失败
```bash
# 检查日志
docker-compose logs magentic-ui-browser

# 增加共享内存
# 在 docker-compose.yml 中设置 shm_size: 2gb
```

### 5. API Key 无效
```bash
# 检查 API Key 格式
echo $OPENAI_API_KEY

# 测试 API 连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

## 📈 性能优化

### 1. 资源限制
```yaml
# 在 docker-compose.yml 中添加
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
```

### 2. 缓存优化
- 启用 Redis 缓存
- 配置浏览器缓存策略
- 使用 CDN 加速静态资源

### 3. 数据库优化
- 定期清理旧数据
- 优化数据库索引
- 配置连接池

## 🔄 更新和维护

### 更新应用
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
./deploy.sh build

# 重启服务
./deploy.sh restart
```

### 定期维护
```bash
# 清理 Docker 资源
docker system prune -f

# 备份数据
./scripts/backup.sh

# 检查日志大小
du -sh /var/lib/docker/containers/*/
```

## 🆘 获取帮助

- **项目文档**: https://github.com/microsoft/magentic-ui
- **问题报告**: https://github.com/microsoft/magentic-ui/issues
- **故障排除**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- **Docker 文档**: [README-DOCKER.md](README-DOCKER.md)

## 🎉 部署成功！

如果您按照以上步骤操作，现在应该可以：

1. ✅ 访问 Magentic-UI 主界面
2. ✅ 创建新的智能体会话
3. ✅ 观看智能体在浏览器中的操作
4. ✅ 与智能体协作完成任务

**开始使用 Magentic-UI 探索人机协作的无限可能吧！** 🚀
