# 使用宿主机 Docker Socket 的配置
# 这是最推荐的方案，功能完整且稳定

services:
  # 主应用服务（使用宿主机 Docker）
  magentic-ui:
    build:
      context: .
      dockerfile: Dockerfile.nodockerindocker
    ports:
      - "8081:8081"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - DATABASE_URI=********************************************/magentic_ui
      - INSIDE_DOCKER=1
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
      # 挂载宿主机 Docker Socket（关键配置）
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
    networks:
      - magentic-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  magentic-network:
    driver: bridge
