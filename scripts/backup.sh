#!/bin/bash

# Magentic-UI 数据备份脚本

set -e

# 配置
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
COMPOSE_FILE="docker-compose.yml"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 创建备份目录
mkdir -p "$BACKUP_DIR"

echo -e "${BLUE}🗄️  开始备份 Magentic-UI 数据...${NC}"

# 备份数据库
if docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
    echo -e "${BLUE}📊 备份 PostgreSQL 数据库...${NC}"
    docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U magentic magentic_ui > "$BACKUP_DIR/database_$TIMESTAMP.sql"
    echo -e "${GREEN}✅ 数据库备份完成: $BACKUP_DIR/database_$TIMESTAMP.sql${NC}"
fi

# 备份工作空间
if [ -d "workspace" ]; then
    echo -e "${BLUE}📁 备份工作空间...${NC}"
    tar -czf "$BACKUP_DIR/workspace_$TIMESTAMP.tar.gz" workspace/
    echo -e "${GREEN}✅ 工作空间备份完成: $BACKUP_DIR/workspace_$TIMESTAMP.tar.gz${NC}"
fi

# 备份配置文件
echo -e "${BLUE}⚙️  备份配置文件...${NC}"
tar -czf "$BACKUP_DIR/config_$TIMESTAMP.tar.gz" config/ .env* docker-compose*.yml 2>/dev/null || true
echo -e "${GREEN}✅ 配置文件备份完成: $BACKUP_DIR/config_$TIMESTAMP.tar.gz${NC}"

# 清理旧备份（保留最近7天）
echo -e "${BLUE}🧹 清理旧备份文件...${NC}"
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete 2>/dev/null || true
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete 2>/dev/null || true

echo -e "${GREEN}🎉 备份完成！${NC}"
echo -e "${YELLOW}备份文件位置: $BACKUP_DIR${NC}"
ls -la "$BACKUP_DIR"
