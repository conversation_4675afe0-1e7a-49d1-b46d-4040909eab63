-- 初始化数据库脚本
-- 为 Magentic-UI 创建必要的数据库结构

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户和权限
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'magentic_readonly') THEN
        CREATE ROLE magentic_readonly;
    END IF;
END
$$;

-- 授予只读权限
GRANT CONNECT ON DATABASE magentic_ui TO magentic_readonly;
GRANT USAGE ON SCHEMA public TO magentic_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO magentic_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO magentic_readonly;

-- 创建索引优化查询性能
-- 这些索引将在应用启动时由 Alembic 自动创建
-- 此处仅作为参考

-- 会话表索引
-- CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
-- CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON sessions(created_at);

-- 运行表索引
-- CREATE INDEX IF NOT EXISTS idx_runs_session_id ON runs(session_id);
-- CREATE INDEX IF NOT EXISTS idx_runs_status ON runs(status);
-- CREATE INDEX IF NOT EXISTS idx_runs_created_at ON runs(created_at);

-- 消息表索引
-- CREATE INDEX IF NOT EXISTS idx_messages_run_id ON messages(run_id);
-- CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- 计划表索引
-- CREATE INDEX IF NOT EXISTS idx_plans_user_id ON plans(user_id);
-- CREATE INDEX IF NOT EXISTS idx_plans_created_at ON plans(created_at);

-- 插入默认配置数据
-- 这些数据将在应用启动时自动创建
