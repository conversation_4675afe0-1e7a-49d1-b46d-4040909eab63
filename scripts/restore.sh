#!/bin/bash

# Magentic-UI 数据恢复脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

BACKUP_DIR="./backups"
COMPOSE_FILE="docker-compose.yml"

# 显示帮助信息
show_help() {
    echo "Magentic-UI 数据恢复脚本"
    echo ""
    echo "使用方法:"
    echo "  ./restore.sh [选项] <备份文件>"
    echo ""
    echo "选项:"
    echo "  -d, --database    恢复数据库"
    echo "  -w, --workspace   恢复工作空间"
    echo "  -c, --config      恢复配置文件"
    echo "  -a, --all         恢复所有数据"
    echo "  -h, --help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  ./restore.sh -d database_20231201_120000.sql"
    echo "  ./restore.sh -w workspace_20231201_120000.tar.gz"
    echo "  ./restore.sh -a  # 恢复最新的备份"
}

# 列出可用备份
list_backups() {
    echo -e "${BLUE}📋 可用的备份文件:${NC}"
    echo ""
    
    if [ -d "$BACKUP_DIR" ]; then
        echo "数据库备份:"
        ls -la "$BACKUP_DIR"/database_*.sql 2>/dev/null | tail -5 || echo "  无数据库备份文件"
        echo ""
        
        echo "工作空间备份:"
        ls -la "$BACKUP_DIR"/workspace_*.tar.gz 2>/dev/null | tail -5 || echo "  无工作空间备份文件"
        echo ""
        
        echo "配置文件备份:"
        ls -la "$BACKUP_DIR"/config_*.tar.gz 2>/dev/null | tail -5 || echo "  无配置文件备份"
    else
        echo "备份目录不存在: $BACKUP_DIR"
    fi
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    echo -e "${BLUE}📊 恢复数据库...${NC}"
    echo -e "${YELLOW}⚠️  这将覆盖现有数据库，确定要继续吗？(y/N)${NC}"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # 停止应用服务
        docker-compose -f "$COMPOSE_FILE" stop magentic-ui || true
        
        # 恢复数据库
        docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U magentic -d magentic_ui -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
        docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U magentic magentic_ui < "$backup_file"
        
        # 重启应用服务
        docker-compose -f "$COMPOSE_FILE" start magentic-ui
        
        echo -e "${GREEN}✅ 数据库恢复完成${NC}"
    else
        echo "取消恢复操作"
    fi
}

# 恢复工作空间
restore_workspace() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    echo -e "${BLUE}📁 恢复工作空间...${NC}"
    echo -e "${YELLOW}⚠️  这将覆盖现有工作空间，确定要继续吗？(y/N)${NC}"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # 备份现有工作空间
        if [ -d "workspace" ]; then
            mv workspace "workspace.backup.$(date +%s)"
        fi
        
        # 恢复工作空间
        tar -xzf "$backup_file"
        
        echo -e "${GREEN}✅ 工作空间恢复完成${NC}"
    else
        echo "取消恢复操作"
    fi
}

# 恢复配置文件
restore_config() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
        return 1
    fi
    
    echo -e "${BLUE}⚙️  恢复配置文件...${NC}"
    echo -e "${YELLOW}⚠️  这将覆盖现有配置，确定要继续吗？(y/N)${NC}"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # 恢复配置文件
        tar -xzf "$backup_file"
        
        echo -e "${GREEN}✅ 配置文件恢复完成${NC}"
    else
        echo "取消恢复操作"
    fi
}

# 恢复所有数据
restore_all() {
    echo -e "${BLUE}🔄 恢复所有数据...${NC}"
    
    # 查找最新的备份文件
    latest_db=$(ls -t "$BACKUP_DIR"/database_*.sql 2>/dev/null | head -1)
    latest_workspace=$(ls -t "$BACKUP_DIR"/workspace_*.tar.gz 2>/dev/null | head -1)
    latest_config=$(ls -t "$BACKUP_DIR"/config_*.tar.gz 2>/dev/null | head -1)
    
    if [ -n "$latest_db" ]; then
        restore_database "$latest_db"
    fi
    
    if [ -n "$latest_workspace" ]; then
        restore_workspace "$latest_workspace"
    fi
    
    if [ -n "$latest_config" ]; then
        restore_config "$latest_config"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        -d|--database)
            if [ -n "$2" ]; then
                restore_database "$2"
            else
                echo -e "${RED}请指定数据库备份文件${NC}"
                exit 1
            fi
            ;;
        -w|--workspace)
            if [ -n "$2" ]; then
                restore_workspace "$2"
            else
                echo -e "${RED}请指定工作空间备份文件${NC}"
                exit 1
            fi
            ;;
        -c|--config)
            if [ -n "$2" ]; then
                restore_config "$2"
            else
                echo -e "${RED}请指定配置文件备份${NC}"
                exit 1
            fi
            ;;
        -a|--all)
            restore_all
            ;;
        -l|--list)
            list_backups
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
