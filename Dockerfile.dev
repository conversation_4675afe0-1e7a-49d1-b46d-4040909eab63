# 开发环境 Dockerfile
FROM python:3.12-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    docker.io \
    curl \
    git \
    build-essential \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv 包管理器
RUN pip install uv

# 创建应用用户
RUN useradd -m -u 1000 magentic && \
    usermod -aG docker magentic

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY pyproject.toml uv.lock ./

# 安装 Python 依赖
RUN uv venv .venv && \
    . .venv/bin/activate && \
    uv sync --all-extras

# 设置环境变量
ENV PATH="/app/.venv/bin:$PATH"
ENV PYTHONPATH="/app/src"
ENV INSIDE_DOCKER=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 切换到应用用户
USER magentic

# 暴露端口
EXPOSE 8081 8000

# 开发模式启动命令
CMD ["python", "-m", "magentic_ui.backend.cli", "--host", "0.0.0.0", "--port", "8081", "--reload"]
