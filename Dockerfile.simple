# 简化版 Dockerfile - 直接从 PyPI 安装
FROM python:3.12-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装 Docker CLI（不安装 Docker 守护进程）
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | apt-key add - && \
    echo "deb [arch=arm64] https://download.docker.com/linux/debian bookworm stable" > /etc/apt/sources.list.d/docker.list && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 magentic && \
    usermod -aG docker magentic

# 设置工作目录
WORKDIR /app

# 安装 Magentic-UI
RUN pip install --no-cache-dir magentic-ui

# 设置环境变量
ENV PYTHONPATH="/app"
ENV INSIDE_DOCKER=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 切换到应用用户
USER magentic

# 暴露端口
EXPOSE 8081

# 启动命令
CMD ["magentic", "ui", "--host", "0.0.0.0", "--port", "8081"]
