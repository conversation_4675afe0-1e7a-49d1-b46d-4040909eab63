#!/bin/bash

# Magentic-UI 模型配置脚本
# 帮助用户选择和配置不同的 AI 模型

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                  Magentic-UI 模型配置向导                     ║"
    echo "║                                                              ║"
    echo "║  选择您想要使用的 AI 模型提供商                                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# 显示模型选项
show_model_options() {
    echo -e "${BLUE}🤖 支持的模型选项:${NC}"
    echo ""
    echo -e "${GREEN}1) Ollama (本地模型) - 🆓 免费推荐${NC}"
    echo "   ✅ 完全免费，无需 API Key"
    echo "   ✅ 数据隐私，本地运行"
    echo "   ✅ 支持 Qwen、Llama 等强大模型"
    echo "   ⚠️  需要先安装 Ollama"
    echo ""
    echo -e "${YELLOW}2) OpenRouter - 💰 付费但便宜${NC}"
    echo "   ✅ 支持 Claude、Gemini、GPT 等多种模型"
    echo "   ✅ 价格比官方 API 便宜"
    echo "   ✅ 统一接口访问多种模型"
    echo "   💳 需要 OpenRouter API Key"
    echo ""
    echo -e "${BLUE}3) OpenAI 官方 - 💰 付费${NC}"
    echo "   ✅ 最新的 GPT 模型"
    echo "   ✅ 稳定可靠"
    echo "   💳 需要 OpenAI API Key"
    echo ""
    echo -e "${PURPLE}4) Azure OpenAI - 💰 企业级${NC}"
    echo "   ✅ 企业级服务"
    echo "   ✅ 数据合规"
    echo "   💳 需要 Azure 订阅"
    echo ""
}

# 配置 Ollama
setup_ollama() {
    echo -e "${GREEN}🦙 配置 Ollama 本地模型${NC}"
    echo ""
    
    # 检查 Ollama 是否已安装
    if ! command -v ollama &> /dev/null; then
        echo -e "${YELLOW}⚠️  Ollama 未安装，正在为您安装...${NC}"
        
        # 检测操作系统
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "检测到 macOS，请手动安装 Ollama:"
            echo "1. 访问 https://ollama.ai/download"
            echo "2. 下载并安装 Ollama for macOS"
            echo "3. 安装完成后重新运行此脚本"
            exit 1
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            curl -fsSL https://ollama.ai/install.sh | sh
        else
            echo "请手动安装 Ollama: https://ollama.ai/download"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Ollama 已安装${NC}"
    
    # 启动 Ollama 服务
    echo -e "${BLUE}🚀 启动 Ollama 服务...${NC}"
    ollama serve &
    sleep 3
    
    # 下载推荐模型
    echo -e "${BLUE}📥 下载推荐模型 (qwen2.5:14b)...${NC}"
    echo "这可能需要几分钟时间，请耐心等待..."
    ollama pull qwen2.5:14b
    
    echo -e "${BLUE}📥 下载轻量级模型 (qwen2.5:7b)...${NC}"
    ollama pull qwen2.5:7b
    
    # 复制配置文件
    cp .env.ollama .env
    cp config/config-ollama.yaml config/config.yaml
    
    echo -e "${GREEN}✅ Ollama 配置完成！${NC}"
    echo ""
    echo -e "${YELLOW}💡 提示: 您可以使用以下命令管理模型:${NC}"
    echo "   ollama list          # 查看已安装模型"
    echo "   ollama pull <model>  # 下载新模型"
    echo "   ollama rm <model>    # 删除模型"
}

# 配置 OpenRouter
setup_openrouter() {
    echo -e "${YELLOW}🌐 配置 OpenRouter${NC}"
    echo ""
    echo "OpenRouter 提供多种模型的统一 API 接口，价格比官方便宜。"
    echo ""
    echo "请访问 https://openrouter.ai/ 注册账号并获取 API Key"
    echo ""
    
    while true; do
        read -p "请输入您的 OpenRouter API Key: " api_key
        if [[ "$api_key" =~ ^sk-.+ ]]; then
            break
        else
            echo -e "${RED}❌ API Key 格式不正确，应该以 'sk-' 开头${NC}"
        fi
    done
    
    # 复制并更新配置文件
    cp .env.openrouter .env
    cp config/config-openrouter.yaml config/config.yaml
    
    # 更新 API Key
    sed -i.bak "s/OPENROUTER_API_KEY=.*/OPENROUTER_API_KEY=$api_key/" .env
    rm -f .env.bak
    
    echo -e "${GREEN}✅ OpenRouter 配置完成！${NC}"
    echo ""
    echo -e "${YELLOW}💡 支持的模型包括:${NC}"
    echo "   - anthropic/claude-3.5-sonnet"
    echo "   - google/gemini-pro"
    echo "   - openai/gpt-4"
    echo "   - meta-llama/llama-3.1-70b"
}

# 配置 OpenAI
setup_openai() {
    echo -e "${BLUE}🤖 配置 OpenAI${NC}"
    echo ""
    echo "请访问 https://platform.openai.com/api-keys 获取 API Key"
    echo ""
    
    while true; do
        read -p "请输入您的 OpenAI API Key: " api_key
        if [[ "$api_key" =~ ^sk-.+ ]]; then
            break
        else
            echo -e "${RED}❌ API Key 格式不正确，应该以 'sk-' 开头${NC}"
        fi
    done
    
    # 复制并更新配置文件
    cp .env.example .env
    cp config/config.yaml config/config.yaml.bak
    
    # 更新 API Key
    sed -i.bak "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$api_key/" .env
    rm -f .env.bak
    
    echo -e "${GREEN}✅ OpenAI 配置完成！${NC}"
}

# 配置 Azure OpenAI
setup_azure() {
    echo -e "${PURPLE}☁️  配置 Azure OpenAI${NC}"
    echo ""
    echo "请提供您的 Azure OpenAI 配置信息:"
    echo ""
    
    read -p "Azure OpenAI Endpoint: " azure_endpoint
    read -p "Azure OpenAI API Key: " azure_api_key
    read -p "Azure Deployment Name: " azure_deployment
    read -p "API Version (默认 2024-10-21): " api_version
    
    api_version=${api_version:-2024-10-21}
    
    # 复制并更新配置文件
    cp .env.example .env
    
    # 更新 Azure 配置
    cat >> .env << EOF

# Azure OpenAI 配置
AZURE_OPENAI_ENDPOINT=$azure_endpoint
AZURE_OPENAI_API_KEY=$azure_api_key
AZURE_OPENAI_API_VERSION=$api_version
AZURE_OPENAI_DEPLOYMENT=$azure_deployment
EOF
    
    # 更新配置文件以使用 Azure
    sed -i.bak 's/# azure_model_config:/azure_model_config:/' config/config.yaml
    sed -i.bak 's/# *provider: AzureOpenAIChatCompletionClient/  provider: AzureOpenAIChatCompletionClient/' config/config.yaml
    rm -f config/config.yaml.bak
    
    echo -e "${GREEN}✅ Azure OpenAI 配置完成！${NC}"
}

# 验证配置
verify_config() {
    echo -e "${BLUE}🔍 验证配置...${NC}"
    
    if [ -f .env ] && [ -f config/config.yaml ]; then
        echo -e "${GREEN}✅ 配置文件创建成功${NC}"
        echo ""
        echo -e "${YELLOW}📋 配置摘要:${NC}"
        echo "   环境文件: .env"
        echo "   配置文件: config/config.yaml"
        echo ""
        echo -e "${GREEN}🚀 现在可以启动 Magentic-UI 了！${NC}"
        echo "   运行: ./deploy.sh start"
    else
        echo -e "${RED}❌ 配置失败，请重试${NC}"
        exit 1
    fi
}

# 主函数
main() {
    show_welcome
    show_model_options
    
    while true; do
        read -p "请选择模型提供商 (1-4): " choice
        case $choice in
            1)
                setup_ollama
                break
                ;;
            2)
                setup_openrouter
                break
                ;;
            3)
                setup_openai
                break
                ;;
            4)
                setup_azure
                break
                ;;
            *)
                echo -e "${RED}请输入 1、2、3 或 4${NC}"
                ;;
        esac
    done
    
    verify_config
}

# 运行主函数
main "$@"
