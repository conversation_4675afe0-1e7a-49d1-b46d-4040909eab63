# Docker-in-Docker 版本的 Docker Compose 配置
# 支持完整的 Magentic-UI 功能

services:
  # 主应用服务（支持 Docker-in-Docker）
  magentic-ui:
    build:
      context: .
      dockerfile: Dockerfile.dind
    ports:
      - "8081:8081"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - DATABASE_URI=********************************************/magentic_ui
      - INSIDE_DOCKER=1
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
      - docker-socket:/var/run/docker.sock
    depends_on:
      - postgres
      - docker
    networks:
      - magentic-network
    restart: unless-stopped

  # Docker-in-Docker 服务
  docker:
    image: docker:27-dind
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=""
    volumes:
      - docker-data:/var/lib/docker
      - docker-socket:/var/run/docker.sock
    networks:
      - magentic-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

volumes:
  postgres_data:
  docker-data:
  docker-socket:

networks:
  magentic-network:
    driver: bridge
