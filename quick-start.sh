#!/bin/bash

# Magentic-UI 快速启动脚本
# 适用于首次部署和快速测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Magentic-UI Docker 部署                    ║"
    echo "║                                                              ║"
    echo "║  🤖 人机协作的智能体系统                                        ║"
    echo "║  🌐 自动化网页任务                                             ║"
    echo "║  💻 代码生成和执行                                             ║"
    echo "║  📁 文件处理和分析                                             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# 检查系统要求
check_requirements() {
    echo -e "${BLUE}🔍 检查系统要求...${NC}"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        echo "请访问 https://docs.docker.com/get-docker/ 安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装${NC}"
        echo "请安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker 服务未运行${NC}"
        echo "请启动 Docker 服务"
        exit 1
    fi
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [ "$total_mem" -lt 4 ]; then
        echo -e "${YELLOW}⚠️  警告: 系统内存少于 4GB，可能影响性能${NC}"
    fi
    
    echo -e "${GREEN}✅ 系统要求检查通过${NC}"
    echo ""
}

# 获取 OpenAI API Key
get_api_key() {
    echo -e "${BLUE}🔑 配置 OpenAI API Key${NC}"
    
    if [ -f .env ] && grep -q "OPENAI_API_KEY=sk-" .env 2>/dev/null; then
        echo -e "${GREEN}✅ 已找到现有的 API Key 配置${NC}"
        return
    fi
    
    echo "请输入您的 OpenAI API Key:"
    echo -e "${YELLOW}提示: API Key 格式为 sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx${NC}"
    read -r api_key
    
    if [[ ! "$api_key" =~ ^sk-.+ ]]; then
        echo -e "${RED}❌ API Key 格式不正确${NC}"
        echo "请确保 API Key 以 'sk-' 开头"
        exit 1
    fi
    
    # 创建 .env 文件
    if [ ! -f .env ]; then
        cp .env.example .env
    fi
    
    # 更新 API Key
    sed -i.bak "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$api_key/" .env
    rm -f .env.bak
    
    echo -e "${GREEN}✅ API Key 配置完成${NC}"
    echo ""
}

# 选择部署模式
choose_deployment_mode() {
    echo -e "${BLUE}🚀 选择部署模式${NC}"
    echo ""
    echo "1) 快速模式 (推荐新手)"
    echo "   - 使用 SQLite 数据库"
    echo "   - 最小资源占用"
    echo "   - 适合测试和开发"
    echo ""
    echo "2) 完整模式 (推荐生产)"
    echo "   - 使用 PostgreSQL 数据库"
    echo "   - 完整功能支持"
    echo "   - 适合生产环境"
    echo ""
    echo "3) 开发模式"
    echo "   - 从源码构建"
    echo "   - 支持热重载"
    echo "   - 适合开发调试"
    echo ""
    
    while true; do
        read -p "请选择部署模式 (1-3): " mode
        case $mode in
            1)
                COMPOSE_FILE="docker-compose.simple.yml"
                MODE_NAME="快速模式"
                break
                ;;
            2)
                COMPOSE_FILE="docker-compose.yml"
                MODE_NAME="完整模式"
                break
                ;;
            3)
                COMPOSE_FILE="docker-compose.dev.yml"
                MODE_NAME="开发模式"
                break
                ;;
            *)
                echo -e "${RED}请输入 1、2 或 3${NC}"
                ;;
        esac
    done
    
    echo -e "${GREEN}✅ 已选择: $MODE_NAME${NC}"
    echo ""
}

# 创建必要目录
create_directories() {
    echo -e "${BLUE}📁 创建工作目录...${NC}"
    
    mkdir -p workspace/{debug,downloads,files,user}
    mkdir -p config
    mkdir -p data/{postgres,redis}
    
    # 设置权限
    chmod 755 workspace
    chmod 755 config
    chmod 755 data
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
    echo ""
}

# 启动服务
start_services() {
    echo -e "${BLUE}🚀 启动 Magentic-UI 服务...${NC}"
    echo "这可能需要几分钟时间，请耐心等待..."
    echo ""
    
    # 构建并启动服务
    if [ "$COMPOSE_FILE" = "docker-compose.simple.yml" ]; then
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        docker-compose -f "$COMPOSE_FILE" build
        docker-compose -f "$COMPOSE_FILE" up -d
    fi
    
    echo ""
    echo -e "${BLUE}⏳ 等待服务启动...${NC}"
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s http://localhost:8081 > /dev/null 2>&1; then
            break
        fi
        echo -n "."
        sleep 2
    done
    echo ""
    
    # 检查服务状态
    if curl -s http://localhost:8081 > /dev/null 2>&1; then
        echo -e "${GREEN}🎉 服务启动成功！${NC}"
        show_access_info
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        echo "请查看日志获取更多信息:"
        echo "docker-compose -f $COMPOSE_FILE logs"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                        🎉 部署成功！                          ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${GREEN}🌐 访问地址:${NC}"
    echo "   主应用:      http://localhost:8081"
    echo "   浏览器 VNC:  http://localhost:6080"
    if [ "$COMPOSE_FILE" = "docker-compose.yml" ]; then
        echo "   数据库:      localhost:5432"
    fi
    echo ""
    echo -e "${BLUE}📋 管理命令:${NC}"
    echo "   查看日志:    docker-compose -f $COMPOSE_FILE logs -f"
    echo "   停止服务:    docker-compose -f $COMPOSE_FILE down"
    echo "   重启服务:    docker-compose -f $COMPOSE_FILE restart"
    echo ""
    echo -e "${YELLOW}💡 使用提示:${NC}"
    echo "   1. 在主应用中创建新会话"
    echo "   2. 输入您的任务描述"
    echo "   3. 在浏览器 VNC 中观看智能体操作"
    echo "   4. 与智能体协作完成任务"
    echo ""
    echo -e "${GREEN}🚀 开始使用 Magentic-UI 吧！${NC}"
}

# 主函数
main() {
    show_welcome
    check_requirements
    get_api_key
    choose_deployment_mode
    create_directories
    start_services
}

# 运行主函数
main "$@"
