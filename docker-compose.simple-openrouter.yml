# 简化版 Docker Compose 配置 - OpenRouter 版本
# 使用 PyPI 安装，避免复杂的构建过程

version: '3.8'

services:
  # 主应用服务（直接从 PyPI 安装）
  magentic-ui:
    image: python:3.12-slim
    ports:
      - "8081:8081"
    environment:
      - OPENROUTER_API_KEY=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
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - INTERNAL_WORKSPACE_ROOT=/workspace
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock
    working_dir: /app
    command: >
      bash -c "
        echo '🚀 正在安装 Magentic-UI...' &&
        apt-get update -qq &&
        apt-get install -y -qq docker.io curl git build-essential &&
        pip install --quiet magentic-ui &&
        echo '✅ 安装完成，正在启动服务...' &&
        echo '🌐 访问地址: http://localhost:8081' &&
        echo '🤖 使用模型: Google Gemini 2.5 Flash (OpenRouter)' &&
        magentic ui --host 0.0.0.0 --port 8081 --config /app/config/config.yaml
      "
    depends_on:
      - postgres
    networks:
      - magentic-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  magentic-network:
    driver: bridge
