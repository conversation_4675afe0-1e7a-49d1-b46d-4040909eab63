# 最简化版 Docker Compose 配置文件
# 只包含主应用和数据库

services:
  # 主应用服务
  magentic-ui:
    build:
      context: .
      dockerfile: Dockerfile.nodockerindocker
    ports:
      - "8081:8081"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - DATABASE_URI=********************************************/magentic_ui
      - INSIDE_DOCKER=1
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
      - DISABLE_DOCKER_FEATURES=1
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
    depends_on:
      - postgres
    networks:
      - magentic-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  magentic-network:
    driver: bridge
