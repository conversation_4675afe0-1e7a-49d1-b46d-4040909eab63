version: '3.8'

services:
  # 主应用服务
  magentic-ui:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URI=********************************************/magentic_ui
      - INSIDE_DOCKER=1
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock  # 允许容器内访问 Docker
    depends_on:
      - postgres
      - magentic-ui-browser
      - magentic-ui-python-env
    networks:
      - magentic-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: registry.cn-hangzhou.aliyuncs.com/library/postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

  # 浏览器服务（VNC + Playwright）
  magentic-ui-browser:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-browser-docker
      dockerfile: Dockerfile
    ports:
      - "6080:6080"    # noVNC Web 界面
      - "37367:37367"  # Playwright 服务器
    environment:
      - DISPLAY=:99
      - PLAYWRIGHT_WS_PATH=default
      - PLAYWRIGHT_PORT=37367
      - NO_VNC_PORT=6080
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    shm_size: 2gb  # 增加共享内存，避免浏览器崩溃

  # Python 执行环境
  magentic-ui-python-env:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-python-env
      dockerfile: Dockerfile
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    command: ["sleep", "infinity"]  # 保持容器运行

  # Redis（可选，用于缓存和会话管理）
  redis:
    image: registry.cn-hangzhou.aliyuncs.com/library/redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - magentic-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  magentic-network:
    driver: bridge
