# Magentic-UI 环境变量配置文件 - Ollama 版本
# 使用本地 Ollama 模型，无需 API Key

# ===================
# Ollama 配置
# ===================
OLLAMA_BASE_URL=http://host.docker.internal:11434
OLLAMA_MODEL=qwen2.5:14b

# ===================
# 数据库配置
# ===================
# 使用 PostgreSQL（推荐生产环境）
DATABASE_URI=********************************************/magentic_ui

# ===================
# 应用配置
# ===================
# 应用主机和端口
_HOST=0.0.0.0
_PORT=8081

# 是否启用 API 文档
_API_DOCS=true

# 应用数据目录
_APPDIR=/app/data

# 是否自动升级数据库
_UPGRADE_DATABASE=1

# ===================
# Docker 配置
# ===================
INSIDE_DOCKER=1
INTERNAL_WORKSPACE_ROOT=/workspace
EXTERNAL_WORKSPACE_ROOT=/workspace

# ===================
# 浏览器配置
# ===================
PLAYWRIGHT_WS_PATH=default
PLAYWRIGHT_PORT=37367
NO_VNC_PORT=6080

# ===================
# 其他配置
# ===================
# 日志级别
LOG_LEVEL=INFO

# 会话超时时间（秒）
SESSION_TIMEOUT=3600

# 最大并发会话数
MAX_CONCURRENT_SESSIONS=5
