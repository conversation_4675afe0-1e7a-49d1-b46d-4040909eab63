# 不使用 Docker-in-Docker 的简化版 Dockerfile
FROM python:3.12-slim

# 安装系统依赖和 Docker CLI
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装 Docker CLI
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 magentic

# 设置工作目录
WORKDIR /app

# 安装 Magentic-UI
RUN pip install --no-cache-dir magentic-ui

# 设置环境变量
ENV PYTHONPATH="/app"
ENV INSIDE_DOCKER=1
ENV DISABLE_DOCKER_FEATURES=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 切换到应用用户
USER magentic

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 Starting Magentic-UI (No Docker Mode)..."\n\
echo "🌐 Access URL: http://localhost:8081"\n\
echo "🤖 AI Model: Google Gemini 2.5 Flash (OpenRouter)"\n\
echo "⚠️  Docker features disabled for container deployment"\n\
echo ""\n\
# 设置环境变量跳过 Docker 检查\n\
export DISABLE_DOCKER_FEATURES=1\n\
export SKIP_DOCKER_CHECK=1\n\
export NO_DOCKER=1\n\
export INSIDE_DOCKER=1\n\
\n\
# 直接启动 Magentic-UI\n\
echo "🔄 Starting Magentic-UI server..."\n\
exec magentic ui --host 0.0.0.0 --port 8081 --no-rebuild-docker\n\
' > /app/start.sh && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8081

# 启动命令
CMD ["/app/start.sh"]
