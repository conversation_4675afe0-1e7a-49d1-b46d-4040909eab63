# 不使用 Docker-in-Docker 的简化版 Dockerfile
FROM python:3.12-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 magentic

# 设置工作目录
WORKDIR /app

# 安装 Magentic-UI
RUN pip install --no-cache-dir magentic-ui

# 设置环境变量
ENV PYTHONPATH="/app"
ENV INSIDE_DOCKER=1
ENV DISABLE_DOCKER_FEATURES=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 切换到应用用户
USER magentic

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 Starting Magentic-UI..."\n\
echo "🌐 Access URL: http://localhost:8081"\n\
echo "🤖 AI Model: Google Gemini 2.5 Flash (OpenRouter)"\n\
echo "⚠️  Running in container mode"\n\
# 尝试启动，如果失败则使用备用方法\n\
if ! timeout 10 magentic ui --host 0.0.0.0 --port 8081 2>/dev/null; then\n\
  echo "⚠️  Standard startup failed, trying alternative method..."\n\
  python -m uvicorn magentic_ui.app:app --host 0.0.0.0 --port 8081 || \\\n\
  python -c "from magentic_ui import app; import uvicorn; uvicorn.run(app, host=\"0.0.0.0\", port=8081)"\n\
fi\n\
' > /app/start.sh && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8081

# 启动命令
CMD ["/app/start.sh"]
