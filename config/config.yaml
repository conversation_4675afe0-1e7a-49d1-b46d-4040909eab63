# Magentic-UI 配置文件
# 此文件包含模型客户端和智能体的详细配置

######################################
# 默认 OpenAI 模型配置
######################################
model_config: &client
  provider: OpenAIChatCompletionClient
  config:
    model: gpt-4o
    api_key: ${OPENAI_API_KEY}
    max_retries: 10

# 动作守卫使用的轻量级模型
model_config_action_guard: &client_action_guard
  provider: OpenAIChatCompletionClient
  config:
    model: gpt-4o-mini
    api_key: ${OPENAI_API_KEY}
    max_retries: 5

##########################
# 各智能体的客户端配置
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client_action_guard

# 计划学习客户端（可选）
plan_learning_client: *client

######################################
# Azure OpenAI 配置示例（注释掉）
######################################
# azure_model_config: &azure_client
#   provider: AzureOpenAIChatCompletionClient
#   config:
#     model: gpt-4o
#     azure_endpoint: "${AZURE_OPENAI_ENDPOINT}"
#     azure_deployment: "${AZURE_OPENAI_DEPLOYMENT}"
#     api_version: "2024-10-21"
#     azure_ad_token_provider:
#       provider: autogen_ext.auth.azure.AzureTokenProvider
#       config:
#         provider_kind: DefaultAzureCredential
#         scopes:
#           - https://cognitiveservices.azure.com/.default
#     max_retries: 10

######################################
# Ollama 配置示例（注释掉）
######################################
# ollama_model_config: &ollama_client
#   provider: OllamaChatCompletionClient
#   config:
#     model: llama2
#     base_url: "${OLLAMA_BASE_URL}"
#     max_retries: 5

######################################
# 智能体行为配置
######################################
agent_config:
  # 协作规划模式
  cooperative_planning: true
  
  # 自主执行模式
  autonomous_execution: false
  
  # 每步最大动作数
  max_actions_per_step: 5
  
  # 是否允许单次调用多个工具
  multiple_tools_per_call: false
  
  # 最大轮次
  max_turns: 20
  
  # 审批策略：always, never, auto-conservative, auto-permissive
  approval_policy: auto-conservative
  
  # 是否允许重新规划
  allow_for_replans: true
  
  # 是否启用 Bing 搜索
  do_bing_search: false
  
  # 是否启用 WebSurfer 循环模式
  websurfer_loop: false
  
  # 计划检索模式：never, hint, reuse
  retrieve_relevant_plans: never

######################################
# 浏览器配置
######################################
browser_config:
  # 是否显示动画
  animate_actions: true
  
  # 单标签模式
  single_tab_mode: false
  
  # 是否保存截图
  to_save_screenshots: true
  
  # 调试目录
  debug_dir: "/workspace/debug"
  
  # 下载目录
  downloads_folder: "/workspace/downloads"

######################################
# 内存和学习配置
######################################
memory_config:
  # 内存控制器密钥
  memory_controller_key: null
  
  # 模型上下文令牌限制
  model_context_token_limit: 110000
  
  # 相关性转换阈值
  relevance_conversion_threshold: 1.7

######################################
# MCP 服务器配置（可选）
######################################
# mcp_servers:
#   - name: "filesystem"
#     command: "npx"
#     args: ["@modelcontextprotocol/server-filesystem", "/workspace"]
#   - name: "brave-search"
#     command: "npx"
#     args: ["@modelcontextprotocol/server-brave-search"]
#     env:
#       BRAVE_API_KEY: "${BRAVE_API_KEY}"
