# Magentic-UI 配置文件 - OpenRouter 版本
# 使用 OpenRouter 访问多种模型（<PERSON>、Gemini 等）

######################################
# OpenRouter 模型配置
######################################
model_config: &client
  provider: OpenAIChatCompletionClient
  config:
    model: "google/gemini-2.5-flash-preview-05-20"  # Google Gemini 2.5 Flash
    base_url: "https://openrouter.ai/api/v1"
    api_key: ${OPENROUTER_API_KEY}
    max_retries: 10

# 轻量级模型用于动作守卫
model_config_action_guard: &client_action_guard
  provider: OpenAIChatCompletionClient
  config:
    model: "google/gemini-2.5-flash-preview-05-20"  # 使用同一模型保持一致性
    base_url: "https://openrouter.ai/api/v1"
    api_key: ${OPENROUTER_API_KEY}
    max_retries: 5

##########################
# 各智能体的客户端配置
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client_action_guard

# 计划学习客户端
plan_learning_client: *client

######################################
# 智能体行为配置
######################################
agent_config:
  # 协作规划模式
  cooperative_planning: true
  
  # 自主执行模式
  autonomous_execution: false
  
  # 每步最大动作数
  max_actions_per_step: 5
  
  # 是否允许单次调用多个工具
  multiple_tools_per_call: false
  
  # 最大轮次
  max_turns: 20
  
  # 审批策略：always, never, auto-conservative, auto-permissive
  approval_policy: auto-conservative
  
  # 是否允许重新规划
  allow_for_replans: true
  
  # 是否启用 Bing 搜索
  do_bing_search: false
  
  # 是否启用 WebSurfer 循环模式
  websurfer_loop: false
  
  # 计划检索模式：never, hint, reuse
  retrieve_relevant_plans: never

######################################
# 浏览器配置
######################################
browser_config:
  # 是否显示动画
  animate_actions: true
  
  # 单标签模式
  single_tab_mode: false
  
  # 是否保存截图
  to_save_screenshots: true
  
  # 调试目录
  debug_dir: "/workspace/debug"
  
  # 下载目录
  downloads_folder: "/workspace/downloads"

######################################
# 内存和学习配置
######################################
memory_config:
  # 内存控制器密钥
  memory_controller_key: null
  
  # 模型上下文令牌限制
  model_context_token_limit: 200000
  
  # 相关性转换阈值
  relevance_conversion_threshold: 1.7
