# Magentic-UI 配置文件 - Ollama 本地模型版本
# 使用免费的本地开源模型

######################################
# Ollama 本地模型配置
######################################
model_config: &client
  provider: OllamaChatCompletionClient
  config:
    model: "qwen2.5:14b"  # 推荐的多模态模型
    base_url: "http://host.docker.internal:11434"  # Docker 内访问宿主机 Ollama
    max_retries: 5

# 轻量级模型用于动作守卫
model_config_action_guard: &client_action_guard
  provider: OllamaChatCompletionClient
  config:
    model: "qwen2.5:7b"  # 更轻量的模型
    base_url: "http://host.docker.internal:11434"
    max_retries: 3

##########################
# 各智能体的客户端配置
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client_action_guard

# 计划学习客户端
plan_learning_client: *client

######################################
# 智能体行为配置
######################################
agent_config:
  # 协作规划模式
  cooperative_planning: true
  
  # 自主执行模式
  autonomous_execution: false
  
  # 每步最大动作数
  max_actions_per_step: 3
  
  # 是否允许单次调用多个工具
  multiple_tools_per_call: false
  
  # 最大轮次
  max_turns: 15
  
  # 审批策略：always, never, auto-conservative, auto-permissive
  approval_policy: auto-conservative
  
  # 是否允许重新规划
  allow_for_replans: true
  
  # 是否启用 Bing 搜索
  do_bing_search: false
  
  # 是否启用 WebSurfer 循环模式
  websurfer_loop: false
  
  # 计划检索模式：never, hint, reuse
  retrieve_relevant_plans: never

######################################
# 浏览器配置
######################################
browser_config:
  # 是否显示动画
  animate_actions: true
  
  # 单标签模式
  single_tab_mode: false
  
  # 是否保存截图
  to_save_screenshots: true
  
  # 调试目录
  debug_dir: "/workspace/debug"
  
  # 下载目录
  downloads_folder: "/workspace/downloads"

######################################
# 内存和学习配置
######################################
memory_config:
  # 内存控制器密钥
  memory_controller_key: null
  
  # 模型上下文令牌限制（Ollama 模型通常较小）
  model_context_token_limit: 32000
  
  # 相关性转换阈值
  relevance_conversion_threshold: 1.5
