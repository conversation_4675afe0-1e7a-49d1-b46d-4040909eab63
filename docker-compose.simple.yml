# 简化版 Docker Compose 配置
# 适用于快速测试和开发环境

version: '3.8'

services:
  # 主应用服务（使用 PyPI 安装）
  magentic-ui:
    image: python:3.12-slim
    ports:
      - "8081:8081"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - INSIDE_DOCKER=0
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
    volumes:
      - ./workspace:/workspace
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock
    working_dir: /app
    command: >
      bash -c "
        apt-get update && 
        apt-get install -y docker.io curl git && 
        pip install magentic-ui[azure,ollama] && 
        magentic ui --host 0.0.0.0 --port 8081
      "
    depends_on:
      - magentic-ui-browser
      - magentic-ui-python-env
    networks:
      - magentic-network
    restart: unless-stopped

  # 浏览器服务
  magentic-ui-browser:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-browser-docker
      dockerfile: Dockerfile
    ports:
      - "6080:6080"    # noVNC Web 界面
      - "37367:37367"  # Playwright 服务器
    environment:
      - DISPLAY=:99
      - PLAYWRIGHT_WS_PATH=default
      - PLAYWRIGHT_PORT=37367
      - NO_VNC_PORT=6080
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    shm_size: 2gb

  # Python 执行环境
  magentic-ui-python-env:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-python-env
      dockerfile: Dockerfile
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    command: ["sleep", "infinity"]

networks:
  magentic-network:
    driver: bridge
