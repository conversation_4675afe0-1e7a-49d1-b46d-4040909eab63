# 开发环境 Docker Compose 配置
# 支持热重载和源码挂载

version: '3.8'

services:
  # 主应用服务（开发模式）
  magentic-ui-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8081:8081"
      - "8000:8000"  # 前端开发服务器
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URI=********************************************/magentic_ui
      - INSIDE_DOCKER=1
      - INTERNAL_WORKSPACE_ROOT=/workspace
      - EXTERNAL_WORKSPACE_ROOT=/workspace
      - _HOST=0.0.0.0
      - _PORT=8081
      - _API_DOCS=true
      - PYTHONPATH=/app/src
      - NODE_ENV=development
    volumes:
      - ./src:/app/src
      - ./frontend:/app/frontend
      - ./config:/app/config
      - ./workspace:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
      - magentic-ui-browser
      - magentic-ui-python-env
    networks:
      - magentic-network
    restart: unless-stopped
    command: >
      bash -c "
        echo 'Starting development server...' &&
        cd /app &&
        python -m magentic_ui.backend.cli --host 0.0.0.0 --port 8081 --reload
      "

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=magentic_ui
      - POSTGRES_USER=magentic
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - magentic-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - magentic-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 浏览器服务
  magentic-ui-browser:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-browser-docker
      dockerfile: Dockerfile
    ports:
      - "6080:6080"
      - "37367:37367"
    environment:
      - DISPLAY=:99
      - PLAYWRIGHT_WS_PATH=default
      - PLAYWRIGHT_PORT=37367
      - NO_VNC_PORT=6080
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    shm_size: 2gb

  # Python 执行环境
  magentic-ui-python-env:
    build:
      context: ./src/magentic_ui/docker/magentic-ui-python-env
      dockerfile: Dockerfile
    volumes:
      - ./workspace:/workspace
    networks:
      - magentic-network
    restart: unless-stopped
    command: ["sleep", "infinity"]

  # 前端开发服务器（可选）
  frontend-dev:
    image: node:18-slim
    working_dir: /app
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - GATSBY_API_URL=http://localhost:8081
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    networks:
      - magentic-network
    command: >
      bash -c "
        npm install &&
        npm run develop -- --host 0.0.0.0 --port 8000
      "
    profiles:
      - frontend  # 使用 --profile frontend 启动

  # 数据库管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - magentic-network
    profiles:
      - tools  # 使用 --profile tools 启动

  # 日志聚合工具
  logs:
    image: gliderlabs/logspout:latest
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "8080:80"
    networks:
      - magentic-network
    profiles:
      - tools
    command: >
      bash -c "
        echo 'Log aggregation available at http://localhost:8080/logs' &&
        /bin/logspout
      "

volumes:
  postgres_dev_data:
  redis_dev_data:
  pgadmin_data:
  frontend_node_modules:

networks:
  magentic-network:
    driver: bridge
