# Git 相关
.git
.gitignore
.gitattributes

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# 文档
docs/_build/

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 前端构建产物
frontend/public/
frontend/.cache/
frontend/node_modules/

# 数据文件
*.db
*.sqlite
*.sqlite3

# 工作空间
workspace/
data/

# Docker 相关
Dockerfile.dev
docker-compose.override.yml

# 配置文件（包含敏感信息）
.env
config.yaml

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他
.mypy_cache/
.dmypy.json
dmypy.json
