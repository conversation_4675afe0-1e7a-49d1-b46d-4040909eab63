# 多阶段构建 Dockerfile for Magentic-UI
FROM node:18-slim AS frontend-builder

# 构建前端
WORKDIR /app/frontend
COPY frontend/package.json frontend/yarn.lock ./
RUN yarn install --frozen-lockfile

COPY frontend/ ./
RUN yarn build

# Python 后端构建阶段
FROM python:3.12-slim AS backend-builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv 包管理器
RUN pip install uv

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY pyproject.toml uv.lock ./
COPY src/ ./src/

# 安装 Python 依赖
RUN uv venv .venv && \
    . .venv/bin/activate && \
    uv sync --all-extras

# 最终运行阶段
FROM python:3.12-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    docker.io \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 magentic && \
    usermod -aG docker magentic

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=backend-builder /app/.venv /app/.venv
COPY --from=backend-builder /app/src /app/src
COPY --from=frontend-builder /app/frontend/public /app/src/magentic_ui/backend/web/ui

# 复制其他必要文件
COPY pyproject.toml ./
COPY src/magentic_ui/docker/ ./src/magentic_ui/docker/

# 设置环境变量
ENV PATH="/app/.venv/bin:$PATH"
ENV PYTHONPATH="/app/src"
ENV INSIDE_DOCKER=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 切换到应用用户
USER magentic

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/ || exit 1

# 启动命令
CMD ["python", "-m", "magentic_ui.backend.cli", "--host", "0.0.0.0", "--port", "8081"]
