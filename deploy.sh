#!/bin/bash

# Magentic-UI Docker 部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|build|clean]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境文件
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，从 .env.example 创建"
        cp .env.example .env
        log_warning "请编辑 .env 文件并设置您的 OPENAI_API_KEY"
        echo "编辑完成后，请重新运行部署脚本"
        exit 1
    fi
    
    # 检查关键环境变量
    if ! grep -q "OPENAI_API_KEY=sk-" .env 2>/dev/null; then
        log_warning "请在 .env 文件中设置有效的 OPENAI_API_KEY"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    mkdir -p workspace/{debug,downloads,files}
    mkdir -p config
    mkdir -p data/{postgres,redis}
    log_success "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    # 构建主应用镜像
    log_info "构建主应用镜像..."
    docker-compose build magentic-ui
    
    # 构建浏览器镜像
    log_info "构建浏览器镜像..."
    docker-compose build magentic-ui-browser
    
    # 构建 Python 环境镜像
    log_info "构建 Python 环境镜像..."
    docker-compose build magentic-ui-python-env
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动 Magentic-UI 服务..."
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功！"
        echo ""
        echo "🌐 访问地址："
        echo "   主应用: http://localhost:8081"
        echo "   浏览器 VNC: http://localhost:6080"
        echo "   数据库: localhost:5432"
        echo ""
        echo "📋 查看日志: ./deploy.sh logs"
        echo "🛑 停止服务: ./deploy.sh stop"
    else
        log_error "服务启动失败，请查看日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止 Magentic-UI 服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 Magentic-UI 服务..."
    docker-compose restart
    log_success "服务已重启"
}

# 查看日志
show_logs() {
    if [ -n "$2" ]; then
        docker-compose logs -f "$2"
    else
        docker-compose logs -f
    fi
}

# 清理资源
clean_resources() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理 Docker 资源..."
        docker-compose down -v --rmi all
        docker system prune -f
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Magentic-UI Docker 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  build     构建 Docker 镜像"
    echo "  logs      查看服务日志"
    echo "  clean     清理所有 Docker 资源"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./deploy.sh start          # 启动服务"
    echo "  ./deploy.sh logs magentic-ui  # 查看主应用日志"
    echo "  ./deploy.sh stop           # 停止服务"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            check_dependencies
            check_env
            create_directories
            build_images
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        build)
            check_dependencies
            build_images
            ;;
        logs)
            show_logs "$@"
            ;;
        clean)
            clean_resources
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
