# Magentic-UI Docker 部署指南

## 🚀 快速开始

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- OpenAI API Key

### 1. 克隆项目

```bash
git clone https://github.com/microsoft/magentic-ui.git
cd magentic-ui
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，设置您的 OpenAI API Key
nano .env
```

**必须设置的环境变量：**
```bash
OPENAI_API_KEY=sk-your-api-key-here
```

### 3. 一键部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 启动服务
./deploy.sh start
```

### 4. 访问应用

- **主应用**: http://localhost:8081
- **浏览器 VNC**: http://localhost:6080 （查看智能体操作浏览器）
- **数据库**: localhost:5432 （如果使用 PostgreSQL）

## 📋 部署选项

### 选项 1：完整部署（推荐生产环境）

使用 PostgreSQL 数据库和完整功能：

```bash
# 使用默认 docker-compose.yml
./deploy.sh start
```

### 选项 2：简化部署（快速测试）

使用 SQLite 数据库，更轻量：

```bash
# 使用简化配置
docker-compose -f docker-compose.simple.yml up -d
```

### 选项 3：开发模式

从源码构建，适合开发调试：

```bash
# 构建开发镜像
docker-compose -f docker-compose.dev.yml up -d
```

## 🛠️ 管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
./deploy.sh logs

# 查看特定服务日志
./deploy.sh logs magentic-ui

# 重启服务
./deploy.sh restart

# 停止服务
./deploy.sh stop

# 清理所有资源
./deploy.sh clean
```

## 🔧 配置说明

### 环境变量配置

主要环境变量在 `.env` 文件中配置：

```bash
# OpenAI API 配置
OPENAI_API_KEY=sk-your-key-here

# 数据库配置
DATABASE_URI=********************************************/magentic_ui

# 应用配置
_HOST=0.0.0.0
_PORT=8081
_API_DOCS=true

# Docker 配置
INSIDE_DOCKER=1
INTERNAL_WORKSPACE_ROOT=/workspace
EXTERNAL_WORKSPACE_ROOT=/workspace
```

### 模型配置

在 `config/config.yaml` 中配置模型和智能体：

```yaml
# OpenAI 配置
model_config: &client
  provider: OpenAIChatCompletionClient
  config:
    model: gpt-4o
    api_key: ${OPENAI_API_KEY}
    max_retries: 10

# 智能体配置
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
```

## 🐛 故障排除

### 常见问题

1. **Docker 权限问题**
   ```bash
   sudo usermod -aG docker $USER
   # 重新登录或重启
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8081
   
   # 修改 .env 中的端口配置
   _PORT=8082
   ```

3. **内存不足**
   ```bash
   # 增加 Docker 内存限制
   # 在 Docker Desktop 设置中调整
   ```

4. **浏览器容器启动失败**
   ```bash
   # 检查共享内存设置
   docker-compose logs magentic-ui-browser
   
   # 增加 shm_size 在 docker-compose.yml 中
   shm_size: 2gb
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f magentic-ui
docker-compose logs -f magentic-ui-browser
docker-compose logs -f postgres

# 查看容器状态
docker-compose ps
```

### 数据备份

```bash
# 备份 PostgreSQL 数据
docker-compose exec postgres pg_dump -U magentic magentic_ui > backup.sql

# 备份工作空间
tar -czf workspace-backup.tar.gz workspace/

# 恢复数据
docker-compose exec -T postgres psql -U magentic magentic_ui < backup.sql
```

## 🔒 安全配置

### 生产环境建议

1. **更改默认密码**
   ```bash
   # 在 .env 中设置强密码
   POSTGRES_PASSWORD=your-strong-password
   ```

2. **限制网络访问**
   ```yaml
   # 在 docker-compose.yml 中
   ports:
     - "127.0.0.1:8081:8081"  # 只允许本地访问
   ```

3. **使用 HTTPS**
   ```bash
   # 配置反向代理（Nginx/Traefik）
   # 或使用 Let's Encrypt 证书
   ```

4. **环境变量安全**
   ```bash
   # 使用 Docker secrets 或外部密钥管理
   # 不要在代码中硬编码敏感信息
   ```

## 📊 监控和维护

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8081/api/health

# 检查数据库连接
docker-compose exec postgres pg_isready -U magentic
```

### 性能监控

```bash
# 查看资源使用情况
docker stats

# 查看磁盘使用
docker system df

# 清理未使用的资源
docker system prune -f
```

### 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
./deploy.sh build

# 重启服务
./deploy.sh restart
```

## 🆘 获取帮助

- **项目文档**: https://github.com/microsoft/magentic-ui
- **问题报告**: https://github.com/microsoft/magentic-ui/issues
- **故障排除**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

## 📝 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。
