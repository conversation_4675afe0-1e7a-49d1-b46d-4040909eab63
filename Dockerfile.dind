# Docker-in-Docker 支持的 Dockerfile
FROM python:3.12-slim

# 安装系统依赖和 Docker CLI
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 安装 Docker CLI
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# 创建应用用户和 docker 组
RUN groupadd docker && \
    useradd -m -u 1000 magentic && \
    usermod -aG docker magentic

# 设置工作目录
WORKDIR /app

# 安装 Magentic-UI
RUN pip install --no-cache-dir magentic-ui

# 设置环境变量
ENV PYTHONPATH="/app"
ENV INSIDE_DOCKER=1

# 创建必要目录
RUN mkdir -p /workspace /app/config && \
    chown -R magentic:magentic /app /workspace

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 Starting Magentic-UI with Docker support..."\n\
echo "🌐 Access URL: http://localhost:8081"\n\
echo "🤖 AI Model: Google Gemini 2.5 Flash (OpenRouter)"\n\
echo "🐳 Docker: Enabled (Docker-in-Docker)"\n\
\n\
# 等待 Docker 服务可用\n\
echo "⏳ Waiting for Docker service..."\n\
until docker info >/dev/null 2>&1; do\n\
  echo "   Docker not ready, waiting..."\n\
  sleep 2\n\
done\n\
echo "✅ Docker service is ready!"\n\
\n\
# 启动 Magentic-UI\n\
exec magentic ui --host 0.0.0.0 --port 8081\n\
' > /app/start.sh && chmod +x /app/start.sh

# 切换到应用用户
USER magentic

# 暴露端口
EXPOSE 8081

# 启动命令
CMD ["/app/start.sh"]
